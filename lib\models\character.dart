class Character {
  final String id;
  final String name;
  final String school;
  final String birthday;
  final String photoUrl;
  final String imageSchool;
  final String damageType;
  final CharacterNames? names;
  final String? age;
  final String? background;
  final String? height;
  final List<String>? hobbies;
  final String? voice;
  final String? voices;
  final List<String>? role;
  final String? armorType;
  final List<Affinity>? affinity;
  final String? weapon;
  final String? weaponUnique;
  final String? weaponImage;
  final String? releaseDate;

  Character({
    required this.id,
    required this.name,
    required this.school,
    required this.birthday,
    required this.photoUrl,
    required this.imageSchool,
    required this.damageType,
    this.names,
    this.age,
    this.background,
    this.height,
    this.hobbies,
    this.voice,
    this.voices,
    this.role,
    this.armorType,
    this.affinity,
    this.weapon,
    this.weaponUnique,
    this.weaponImage,
    this.releaseDate,
  });

  factory Character.fromJson(Map<String, dynamic> json) {
    return Character(
      id: json['_id'] ?? '',
      name: json['name'] ?? '',
      school: json['school'] ?? '',
      birthday: json['birthday'] ?? '',
      photoUrl: json['photoUrl'] ?? '',
      imageSchool: json['imageSchool'] ?? '',
      damageType: json['damageType'] ?? '',
      names: json['names'] != null ? CharacterNames.fromJson(json['names']) : null,
      age: json['age'],
      background: json['background'],
      height: json['height'],
      hobbies: json['hobbies'] != null ? List<String>.from(json['hobbies']) : null,
      voice: json['voice'],
      voices: json['voices'],
      role: json['role'] != null ? List<String>.from(json['role']) : null,
      armorType: json['armorType'],
      affinity: json['affinity'] != null 
          ? (json['affinity'] as List).map((a) => Affinity.fromJson(a)).toList()
          : null,
      weapon: json['weapon'],
      weaponUnique: json['weaponUnique'],
      weaponImage: json['weaponImage'],
      releaseDate: json['realeaseDate'], // Note: API has typo "realeaseDate"
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'school': school,
      'birthday': birthday,
      'photoUrl': photoUrl,
      'imageSchool': imageSchool,
      'damageType': damageType,
      'names': names?.toJson(),
      'age': age,
      'background': background,
      'height': height,
      'hobbies': hobbies,
      'voice': voice,
      'voices': voices,
      'role': role,
      'armorType': armorType,
      'affinity': affinity?.map((a) => a.toJson()).toList(),
      'weapon': weapon,
      'weaponUnique': weaponUnique,
      'weaponImage': weaponImage,
      'realeaseDate': releaseDate,
    };
  }
}

class CharacterNames {
  final String firstName;
  final String lastName;
  final String japanName;

  CharacterNames({
    required this.firstName,
    required this.lastName,
    required this.japanName,
  });

  factory CharacterNames.fromJson(Map<String, dynamic> json) {
    return CharacterNames(
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      japanName: json['japanName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'japanName': japanName,
    };
  }
}

class Affinity {
  final String? urban;
  final String? urbanEmotion;
  final String? outdoors;
  final String? outdoorsEmotion;
  final String? indoors;
  final String? indoorsEmotion;

  Affinity({
    this.urban,
    this.urbanEmotion,
    this.outdoors,
    this.outdoorsEmotion,
    this.indoors,
    this.indoorsEmotion,
  });

  factory Affinity.fromJson(Map<String, dynamic> json) {
    return Affinity(
      urban: json['urban'],
      urbanEmotion: json['urbanEmotion'],
      outdoors: json['outdoors'],
      outdoorsEmotion: json['outdoorsEmotion'],
      indoors: json['indoors'],
      indoorsEmotion: json['indoorsEmotion'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'urban': urban,
      'urbanEmotion': urbanEmotion,
      'outdoors': outdoors,
      'outdoorsEmotion': outdoorsEmotion,
      'indoors': indoors,
      'indoorsEmotion': indoorsEmotion,
    };
  }
}

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? error;
  final int? totalCount;
  final int? currentPage;
  final int? totalPages;

  ApiResponse({
    required this.success,
    this.data,
    this.error,
    this.totalCount,
    this.currentPage,
    this.totalPages,
  });
}
