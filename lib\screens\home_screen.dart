import 'package:flutter/material.dart';
import '../models/character.dart';
import '../services/api_service.dart';
import '../utils/constants.dart';
import '../widgets/character_card.dart';
import '../widgets/loading_widget.dart' as widgets;
import 'search_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final ApiService _apiService = ApiService();

  // All Characters Tab
  List<Character> _allCharacters = [];
  bool _isLoadingAll = false;
  String? _errorMessageAll;
  int _currentPage = 1;
  bool _hasMoreData = true;
  final ScrollController _scrollController = ScrollController();

  // Students Tab
  List<Character> _students = [];
  bool _isLoadingStudents = false;
  String? _errorMessageStudents;

  // Random Character
  Character? _randomCharacter;
  bool _isLoadingRandom = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingAll && _hasMoreData && _tabController.index == 0) {
        _loadMoreCharacters();
      }
    }
  }

  Future<void> _loadInitialData() async {
    await Future.wait([
      _loadAllCharacters(refresh: true),
      _loadStudents(),
      _loadRandomCharacter(),
    ]);
  }

  Future<void> _loadAllCharacters({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _currentPage = 1;
        _allCharacters = [];
        _hasMoreData = true;
      });
    }

    setState(() {
      _isLoadingAll = true;
      _errorMessageAll = null;
    });

    try {
      final response = await _apiService.getCharacters(
        page: _currentPage,
        perPage: AppConstants.defaultPageSize,
      );

      setState(() {
        _isLoadingAll = false;
        if (response.success && response.data != null) {
          if (refresh) {
            _allCharacters = response.data!;
          } else {
            _allCharacters.addAll(response.data!);
          }

          if (response.data!.length < AppConstants.defaultPageSize) {
            _hasMoreData = false;
          }
        } else {
          _errorMessageAll = response.error ?? 'Failed to load characters';
        }
      });
    } catch (e) {
      setState(() {
        _isLoadingAll = false;
        _errorMessageAll = 'An error occurred while loading characters';
      });
    }
  }

  Future<void> _loadMoreCharacters() async {
    _currentPage++;
    await _loadAllCharacters();
  }

  Future<void> _loadStudents() async {
    setState(() {
      _isLoadingStudents = true;
      _errorMessageStudents = null;
    });

    try {
      final response = await _apiService.getStudents(perPage: 10);

      setState(() {
        _isLoadingStudents = false;
        if (response.success && response.data != null) {
          _students = response.data!;
        } else {
          _errorMessageStudents = response.error ?? 'Failed to load students';
        }
      });
    } catch (e) {
      setState(() {
        _isLoadingStudents = false;
        _errorMessageStudents = 'An error occurred while loading students';
      });
    }
  }

  Future<void> _loadRandomCharacter() async {
    setState(() {
      _isLoadingRandom = true;
    });

    try {
      final response = await _apiService.getRandomCharacters(count: 1);

      setState(() {
        _isLoadingRandom = false;
        if (response.success &&
            response.data != null &&
            response.data!.isNotEmpty) {
          _randomCharacter = response.data!.first;
        }
      });
    } catch (e) {
      setState(() {
        _isLoadingRandom = false;
      });
    }
  }

  Future<void> _refreshData() async {
    switch (_tabController.index) {
      case 0:
        await _loadAllCharacters(refresh: true);
        break;
      case 1:
        await _loadStudents();
        break;
      case 2:
        await _loadRandomCharacter();
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          AppConstants.appName,
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(AppColors.primaryBlue),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SearchScreen()),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'All Characters'),
            Tab(text: 'Students'),
            Tab(text: 'Random'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllCharactersTab(),
          _buildStudentsTab(),
          _buildRandomTab(),
        ],
      ),
    );
  }

  Widget _buildAllCharactersTab() {
    if (_isLoadingAll && _allCharacters.isEmpty) {
      return const widgets.LoadingWidget(message: 'Loading characters...');
    }

    if (_errorMessageAll != null && _allCharacters.isEmpty) {
      return widgets.ErrorWidget(
        message: _errorMessageAll!,
        onRetry: () => _loadAllCharacters(refresh: true),
      );
    }

    if (_allCharacters.isEmpty) {
      return const widgets.EmptyStateWidget(
        title: 'No Characters Found',
        subtitle: 'Unable to load characters at the moment',
        icon: Icons.people_outline,
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshData,
      color: const Color(AppColors.primaryBlue),
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: _allCharacters.length + (_hasMoreData ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _allCharacters.length) {
            return const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Color(AppColors.primaryBlue),
                  ),
                ),
              ),
            );
          }

          return CharacterCard(character: _allCharacters[index]);
        },
      ),
    );
  }

  Widget _buildStudentsTab() {
    if (_isLoadingStudents) {
      return const widgets.LoadingWidget(
        message: 'Loading detailed student information...',
      );
    }

    if (_errorMessageStudents != null) {
      return widgets.ErrorWidget(
        message: _errorMessageStudents!,
        onRetry: _loadStudents,
      );
    }

    if (_students.isEmpty) {
      return const widgets.EmptyStateWidget(
        title: 'No Students Found',
        subtitle: 'Unable to load student information at the moment',
        icon: Icons.school_outlined,
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshData,
      color: const Color(AppColors.primaryBlue),
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 8),
        itemCount: _students.length,
        itemBuilder: (context, index) {
          return CharacterCard(character: _students[index]);
        },
      ),
    );
  }

  Widget _buildRandomTab() {
    return RefreshIndicator(
      onRefresh: _refreshData,
      color: const Color(AppColors.primaryBlue),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Text(
                      'Discover Random Character',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(AppColors.textPrimary),
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Tap the button below to discover a random Blue Archive character!',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(AppColors.textSecondary),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: _isLoadingRandom ? null : _loadRandomCharacter,
                      icon: _isLoadingRandom
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : const Icon(Icons.shuffle),
                      label: Text(
                        _isLoadingRandom
                            ? 'Loading...'
                            : 'Get Random Character',
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(AppColors.primaryBlue),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (_randomCharacter != null) ...[
              CharacterCard(character: _randomCharacter!),
            ] else if (!_isLoadingRandom) ...[
              const widgets.EmptyStateWidget(
                title: 'No Random Character',
                subtitle: 'Tap the button above to get a random character',
                icon: Icons.casino_outlined,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
