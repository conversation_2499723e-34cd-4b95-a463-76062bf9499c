import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'screens/home_screen.dart';
import 'utils/constants.dart';

void main() {
  runApp(const BlueArchiveApp());
}

class BlueArchiveApp extends StatelessWidget {
  const BlueArchiveApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: MaterialColor(AppColors.primaryBlue, <int, Color>{
          50: Color.fromARGB(25, 33, 150, 243),
          100: Color.fromARGB(51, 33, 150, 243),
          200: Color.fromARGB(76, 33, 150, 243),
          300: Color.fromARGB(102, 33, 150, 243),
          400: Color.fromARGB(127, 33, 150, 243),
          500: const Color(AppColors.primaryBlue),
          600: Color.fromARGB(178, 33, 150, 243),
          700: Color.fromARGB(204, 33, 150, 243),
          800: Color.fromARGB(229, 33, 150, 243),
          900: const Color(AppColors.primaryBlue),
        }),
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(AppColors.primaryBlue),
          brightness: Brightness.light,
        ),
        scaffoldBackgroundColor: const Color(AppColors.backgroundColor),
        cardTheme: const CardThemeData(
          color: Color(AppColors.cardColor),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(AppColors.primaryBlue),
          foregroundColor: Colors.white,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.light,
        ),
        textTheme: const TextTheme(
          bodyLarge: TextStyle(color: Color(AppColors.textPrimary)),
          bodyMedium: TextStyle(color: Color(AppColors.textPrimary)),
          titleLarge: TextStyle(
            color: Color(AppColors.textPrimary),
            fontWeight: FontWeight.bold,
          ),
        ),
        useMaterial3: true,
      ),
      home: const HomeScreen(),
    );
  }
}
