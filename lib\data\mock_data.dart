import '../models/character.dart';

class MockData {
  static List<Character> getMockCharacters() {
    return [
      Character(
        id: '1',
        name: '<PERSON><PERSON><PERSON>',
        school: 'Aby<PERSON>',
        birthday: 'August 26',
        photoUrl: 'https://static.miraheze.org/bluearchivewiki/thumb/c/cd/Shiroko.png/266px-Shiroko.png',
        imageSchool: 'https://static.miraheze.org/bluearchivewiki/thumb/f/f3/Abydos.png/50px-Abydos.png',
        damageType: 'Penetration',
        age: '16',
        background: 'A student of Abydos High School and a member of the Foreclosure Task Force. <PERSON><PERSON><PERSON> is a quiet girl who rarely shows her emotions, but she is deeply caring towards her friends and will do anything to protect them.',
        height: '156cm',
        hobbies: ['Cycling', 'Maintenance'],
        voice: '<PERSON><PERSON>',
        role: ['Striker', 'Front'],
        armorType: 'Light',
        weapon: 'AR',
        weaponUnique: 'Assault Rifle',
        releaseDate: '2021/02/04',
      ),
      Character(
        id: '2',
        name: '<PERSON><PERSON>',
        school: 'Aby<PERSON>',
        birthday: 'June 12',
        photoUrl: 'https://static.miraheze.org/bluearchivewiki/thumb/4/4f/<PERSON>ika.png/266px-Serika.png',
        imageSchool: 'https://static.miraheze.org/bluearchivewiki/thumb/f/f3/Abydos.png/50px-Abydos.png',
        damageType: 'Explosive',
        age: '17',
        background: 'A student of Abydos High School and the president of the Foreclosure Task Force. Serika is energetic and optimistic, always trying to keep everyone\'s spirits up despite their difficult situation.',
        height: '161cm',
        hobbies: ['Cooking', 'Cleaning'],
        voice: 'Hanazawa Kana',
        role: ['Striker', 'Front'],
        armorType: 'Light',
        weapon: 'GL',
        weaponUnique: 'Grenade Launcher',
        releaseDate: '2021/02/04',
      ),
      Character(
        id: '3',
        name: 'Nonomi',
        school: 'Abydos',
        birthday: 'March 25',
        photoUrl: 'https://static.miraheze.org/bluearchivewiki/thumb/b/b8/Nonomi.png/266px-Nonomi.png',
        imageSchool: 'https://static.miraheze.org/bluearchivewiki/thumb/f/f3/Abydos.png/50px-Abydos.png',
        damageType: 'Penetration',
        age: '17',
        background: 'A student of Abydos High School and a member of the Foreclosure Task Force. Nonomi is gentle and kind-hearted, often acting as the mediator of the group.',
        height: '159cm',
        hobbies: ['Reading', 'Tea ceremony'],
        voice: 'Ogura Yui',
        role: ['Striker', 'Back'],
        armorType: 'Light',
        weapon: 'SR',
        weaponUnique: 'Sniper Rifle',
        releaseDate: '2021/02/04',
      ),
      Character(
        id: '4',
        name: 'Ayane',
        school: 'Abydos',
        birthday: 'November 3',
        photoUrl: 'https://static.miraheze.org/bluearchivewiki/thumb/a/a7/Ayane.png/266px-Ayane.png',
        imageSchool: 'https://static.miraheze.org/bluearchivewiki/thumb/f/f3/Abydos.png/50px-Abydos.png',
        damageType: 'Mystic',
        age: '16',
        background: 'A student of Abydos High School and a member of the Foreclosure Task Force. Ayane is the treasurer and is very serious about money matters.',
        height: '154cm',
        hobbies: ['Accounting', 'Budgeting'],
        voice: 'Kuno Misaki',
        role: ['Special', 'Back'],
        armorType: 'Light',
        weapon: 'SMG',
        weaponUnique: 'Submachine Gun',
        releaseDate: '2021/02/04',
      ),
      Character(
        id: '5',
        name: 'Hoshino',
        school: 'Abydos',
        birthday: 'January 2',
        photoUrl: 'https://static.miraheze.org/bluearchivewiki/thumb/3/3e/Hoshino.png/266px-Hoshino.png',
        imageSchool: 'https://static.miraheze.org/bluearchivewiki/thumb/f/f3/Abydos.png/50px-Abydos.png',
        damageType: 'Penetration',
        age: '17',
        background: 'A student of Abydos High School and the former president of the Student Council. Hoshino is laid-back and often sleeps, but she\'s reliable when it matters.',
        height: '164cm',
        hobbies: ['Sleeping', 'Stargazing'],
        voice: 'Hanazawa Kana',
        role: ['Tank', 'Front'],
        armorType: 'Heavy',
        weapon: 'SG',
        weaponUnique: 'Shotgun',
        releaseDate: '2021/02/04',
      ),
      Character(
        id: '6',
        name: 'Iori',
        school: 'Gehenna',
        birthday: 'November 8',
        photoUrl: 'https://static.miraheze.org/bluearchivewiki/thumb/2/26/Iori.png/266px-Iori.png',
        imageSchool: 'https://static.miraheze.org/bluearchivewiki/thumb/b/bd/Gehenna.png/50px-Gehenna.png',
        damageType: 'Penetration',
        age: '16',
        background: 'A student of Gehenna Academy and a member of the Disciplinary Committee. Iori is serious and dedicated to maintaining order.',
        height: '158cm',
        hobbies: ['Training', 'Reading regulations'],
        voice: 'Touyama Nao',
        role: ['Striker', 'Front'],
        armorType: 'Heavy',
        weapon: 'AR',
        weaponUnique: 'Assault Rifle',
        releaseDate: '2021/02/04',
      ),
      Character(
        id: '7',
        name: 'Aru',
        school: 'Gehenna',
        birthday: 'March 12',
        photoUrl: 'https://static.miraheze.org/bluearchivewiki/thumb/d/db/Aru.png/266px-Aru.png',
        imageSchool: 'https://static.miraheze.org/bluearchivewiki/thumb/b/bd/Gehenna.png/50px-Gehenna.png',
        damageType: 'Explosive',
        age: '16',
        background: 'A student of Gehenna Academy and the self-proclaimed leader of the Problem Solver 68. Aru is energetic and always looking for trouble to solve.',
        height: '161cm',
        hobbies: ['Problem solving', 'Adventure'],
        voice: 'Hondo Kaede',
        role: ['Striker', 'Back'],
        armorType: 'Light',
        weapon: 'SR',
        weaponUnique: 'Sniper Rifle',
        releaseDate: '2021/02/04',
      ),
      Character(
        id: '8',
        name: 'Yuuka',
        school: 'Millennium',
        birthday: 'April 7',
        photoUrl: 'https://static.miraheze.org/bluearchivewiki/thumb/1/15/Yuuka.png/266px-Yuuka.png',
        imageSchool: 'https://static.miraheze.org/bluearchivewiki/thumb/1/1f/Millennium.png/50px-Millennium.png',
        damageType: 'Mystic',
        age: '17',
        background: 'A student of Millennium Science School and the treasurer of the Student Council. Yuuka is calm and calculating, with a sharp mind for numbers.',
        height: '165cm',
        hobbies: ['Mathematics', 'Economics'],
        voice: 'Komatsu Mikako',
        role: ['Special', 'Back'],
        armorType: 'Light',
        weapon: 'AR',
        weaponUnique: 'Assault Rifle',
        releaseDate: '2021/02/04',
      ),
      Character(
        id: '9',
        name: 'Tsurugi',
        school: 'Trinity',
        birthday: 'September 15',
        photoUrl: 'https://static.miraheze.org/bluearchivewiki/thumb/8/8a/Tsurugi.png/266px-Tsurugi.png',
        imageSchool: 'https://static.miraheze.org/bluearchivewiki/thumb/9/9c/Trinity.png/50px-Trinity.png',
        damageType: 'Penetration',
        age: '16',
        background: 'A student of Trinity General School and a member of the Justice Task Force. Tsurugi is dedicated to justice and protecting the innocent.',
        height: '162cm',
        hobbies: ['Training', 'Justice'],
        voice: 'Itou Shizuka',
        role: ['Striker', 'Front'],
        armorType: 'Heavy',
        weapon: 'SG',
        weaponUnique: 'Shotgun',
        releaseDate: '2021/02/04',
      ),
      Character(
        id: '10',
        name: 'Hasumi',
        school: 'Trinity',
        birthday: 'May 20',
        photoUrl: 'https://static.miraheze.org/bluearchivewiki/thumb/f/f4/Hasumi.png/266px-Hasumi.png',
        imageSchool: 'https://static.miraheze.org/bluearchivewiki/thumb/9/9c/Trinity.png/50px-Trinity.png',
        damageType: 'Penetration',
        age: '17',
        background: 'A student of Trinity General School and the president of the Justice Task Force. Hasumi is a natural leader with a strong sense of justice.',
        height: '168cm',
        hobbies: ['Leadership', 'Strategy'],
        voice: 'Hayami Saori',
        role: ['Striker', 'Front'],
        armorType: 'Light',
        weapon: 'AR',
        weaponUnique: 'Assault Rifle',
        releaseDate: '2021/02/04',
      ),
    ];
  }

  static List<Character> getRandomCharacters(int count) {
    final characters = getMockCharacters();
    characters.shuffle();
    return characters.take(count).toList();
  }

  static List<Character> searchCharacters({
    String? name,
    String? school,
    String? damageType,
  }) {
    var characters = getMockCharacters();
    
    if (name != null && name.isNotEmpty) {
      characters = characters.where((c) => 
        c.name.toLowerCase().contains(name.toLowerCase())).toList();
    }
    
    if (school != null && school.isNotEmpty) {
      characters = characters.where((c) => 
        c.school.toLowerCase() == school.toLowerCase()).toList();
    }
    
    if (damageType != null && damageType.isNotEmpty) {
      characters = characters.where((c) => 
        c.damageType.toLowerCase() == damageType.toLowerCase()).toList();
    }
    
    return characters;
  }
}
