class ApiConstants {
  // Use CORS proxy for web to avoid CORS issues
  static const String baseUrl = 'https://api-blue-archive.vercel.app';
  static const String corsProxy = 'https://cors-anywhere.herokuapp.com/';
  static const String charactersEndpoint = '/api/characters';
  static const String randomCharacterEndpoint = '/api/character/random';
  static const String studentsEndpoint = '/api/characters/students';

  // Get the full URL with CORS proxy for web
  static String getApiUrl(String endpoint) {
    // For web, we'll use a different approach - try direct first, then fallback
    return '$baseUrl$endpoint';
  }
}

class AppConstants {
  static const String appName = 'Blue Archive Characters';
  static const int defaultPageSize = 20;
  static const int maxRetries = 3;
}

class AppColors {
  static const int primaryBlue = 0xFF2196F3;
  static const int secondaryBlue = 0xFF1976D2;
  static const int accentColor = 0xFFFF4081;
  static const int backgroundColor = 0xFFF5F5F5;
  static const int cardColor = 0xFFFFFFFF;
  static const int textPrimary = 0xFF212121;
  static const int textSecondary = 0xFF757575;
}

class DamageTypes {
  static const String explosive = 'Explosive';
  static const String penetration = 'Penetration';
  static const String mystic = 'Mystic';
}

class Schools {
  static const List<String> allSchools = [
    'Trinity',
    'Gehenna',
    'Millennium',
    'Abydos',
    'Hyakkiyako',
    'Shanhaijing',
    'Red Winter',
    'Valkyrie',
    'Arius',
    'SRT',
  ];
}
