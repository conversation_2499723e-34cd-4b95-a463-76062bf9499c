import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/character.dart';
import '../utils/constants.dart';
import '../screens/character_detail_screen.dart';

class CharacterCard extends StatelessWidget {
  final Character character;
  final bool isCompact;

  const CharacterCard({
    super.key,
    required this.character,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CharacterDetailScreen(character: character),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(12),
          child: isCompact ? _buildCompactLayout() : _buildFullLayout(),
        ),
      ),
    );
  }

  Widget _buildCompactLayout() {
    return Row(
      children: [
        _buildCharacterImage(60),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                character.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(AppColors.textPrimary),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              _buildSchoolRow(),
              const SizedBox(height: 4),
              _buildDamageTypeChip(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFullLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            _buildCharacterImage(80),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    character.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(AppColors.textPrimary),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  _buildSchoolRow(),
                  const SizedBox(height: 8),
                  _buildInfoRow(Icons.cake, character.birthday),
                  if (character.age != null) ...[
                    const SizedBox(height: 4),
                    _buildInfoRow(Icons.person, '${character.age} years old'),
                  ],
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            _buildDamageTypeChip(),
            const Spacer(),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Color(AppColors.textSecondary),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCharacterImage(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(AppColors.primaryBlue).withOpacity(0.3),
          width: 2,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: CachedNetworkImage(
          imageUrl: character.photoUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: const Color(AppColors.backgroundColor),
            child: const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Color(AppColors.primaryBlue),
                ),
              ),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            color: const Color(AppColors.backgroundColor),
            child: const Icon(
              Icons.person,
              color: Color(AppColors.textSecondary),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSchoolRow() {
    return Row(
      children: [
        if (character.imageSchool.isNotEmpty) ...[
          CachedNetworkImage(
            imageUrl: character.imageSchool,
            width: 20,
            height: 20,
            placeholder: (context, url) => const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 1),
            ),
            errorWidget: (context, url, error) => const Icon(
              Icons.school,
              size: 20,
              color: Color(AppColors.textSecondary),
            ),
          ),
          const SizedBox(width: 6),
        ],
        Expanded(
          child: Text(
            character.school,
            style: const TextStyle(
              fontSize: 14,
              color: Color(AppColors.textSecondary),
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: const Color(AppColors.textSecondary),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 12,
              color: Color(AppColors.textSecondary),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildDamageTypeChip() {
    Color chipColor;
    switch (character.damageType.toLowerCase()) {
      case 'explosive':
        chipColor = Colors.orange;
        break;
      case 'penetration':
        chipColor = Colors.red;
        break;
      case 'mystic':
        chipColor = Colors.purple;
        break;
      default:
        chipColor = const Color(AppColors.primaryBlue);
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withOpacity(0.3)),
      ),
      child: Text(
        character.damageType,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: chipColor,
        ),
      ),
    );
  }
}
