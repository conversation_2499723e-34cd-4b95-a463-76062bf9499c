import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/character.dart';
import '../utils/constants.dart';

class CharacterDetailScreen extends StatelessWidget {
  final Character character;

  const CharacterDetailScreen({
    super.key,
    required this.character,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(context),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildBasicInfo(),
                  const SizedBox(height: 24),
                  if (character.background != null) ...[
                    _buildBackgroundSection(),
                    const SizedBox(height: 24),
                  ],
                  _buildDetailsSection(),
                  const SizedBox(height: 24),
                  if (character.role != null && character.role!.isNotEmpty) ...[
                    _buildRoleSection(),
                    const SizedBox(height: 24),
                  ],
                  if (character.hobbies != null && character.hobbies!.isNotEmpty) ...[
                    _buildHobbiesSection(),
                    const SizedBox(height: 24),
                  ],
                  if (character.weapon != null) ...[
                    _buildWeaponSection(),
                    const SizedBox(height: 24),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: const Color(AppColors.primaryBlue),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          character.name,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Stack(
          fit: StackFit.expand,
          children: [
            CachedNetworkImage(
              imageUrl: character.photoUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: const Color(AppColors.backgroundColor),
                child: const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: const Color(AppColors.backgroundColor),
                child: const Icon(
                  Icons.person,
                  size: 100,
                  color: Color(AppColors.textSecondary),
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (character.imageSchool.isNotEmpty) ...[
                  CachedNetworkImage(
                    imageUrl: character.imageSchool,
                    width: 30,
                    height: 30,
                    errorWidget: (context, url, error) => const Icon(
                      Icons.school,
                      size: 30,
                      color: Color(AppColors.textSecondary),
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Text(
                    character.school,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(AppColors.textPrimary),
                    ),
                  ),
                ),
                _buildDamageTypeChip(),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(Icons.cake, 'Birthday', character.birthday),
            if (character.age != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow(Icons.person, 'Age', '${character.age} years old'),
            ],
            if (character.height != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow(Icons.height, 'Height', character.height!),
            ],
            if (character.voice != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow(Icons.record_voice_over, 'Voice Actor', character.voice!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBackgroundSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Background',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(AppColors.textPrimary),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              character.background!,
              style: const TextStyle(
                fontSize: 14,
                color: Color(AppColors.textSecondary),
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Combat Details',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(AppColors.textPrimary),
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(Icons.flash_on, 'Damage Type', character.damageType),
            if (character.armorType != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow(Icons.shield, 'Armor Type', character.armorType!),
            ],
            if (character.releaseDate != null) ...[
              const SizedBox(height: 8),
              _buildInfoRow(Icons.calendar_today, 'Release Date', character.releaseDate!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRoleSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Roles',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(AppColors.textPrimary),
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: character.role!.map((role) => _buildRoleChip(role)).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHobbiesSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Hobbies',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(AppColors.textPrimary),
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: character.hobbies!.map((hobby) => _buildHobbyChip(hobby)).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeaponSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Weapon',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(AppColors.textPrimary),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                if (character.weaponImage != null) ...[
                  CachedNetworkImage(
                    imageUrl: character.weaponImage!,
                    width: 40,
                    height: 40,
                    errorWidget: (context, url, error) => const Icon(
                      Icons.sports_esports,
                      size: 40,
                      color: Color(AppColors.textSecondary),
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        character.weapon!,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(AppColors.textPrimary),
                        ),
                      ),
                      if (character.weaponUnique != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          character.weaponUnique!,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(AppColors.textSecondary),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: const Color(AppColors.primaryBlue),
        ),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(AppColors.textPrimary),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Color(AppColors.textSecondary),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDamageTypeChip() {
    Color chipColor;
    switch (character.damageType.toLowerCase()) {
      case 'explosive':
        chipColor = Colors.orange;
        break;
      case 'penetration':
        chipColor = Colors.red;
        break;
      case 'mystic':
        chipColor = Colors.purple;
        break;
      default:
        chipColor = const Color(AppColors.primaryBlue);
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: chipColor.withOpacity(0.3)),
      ),
      child: Text(
        character.damageType,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: chipColor,
        ),
      ),
    );
  }

  Widget _buildRoleChip(String role) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(AppColors.primaryBlue).withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(AppColors.primaryBlue).withOpacity(0.3),
        ),
      ),
      child: Text(
        role,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: Color(AppColors.primaryBlue),
        ),
      ),
    );
  }

  Widget _buildHobbyChip(String hobby) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: const Color(AppColors.accentColor).withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(AppColors.accentColor).withOpacity(0.3),
        ),
      ),
      child: Text(
        hobby,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: Color(AppColors.accentColor),
        ),
      ),
    );
  }
}
