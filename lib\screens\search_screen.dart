import 'package:flutter/material.dart';
import '../models/character.dart';
import '../services/api_service.dart';
import '../utils/constants.dart';
import '../widgets/character_card.dart';
import '../widgets/loading_widget.dart' as widgets;

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ApiService _apiService = ApiService();

  List<Character> _searchResults = [];
  bool _isLoading = false;
  String? _errorMessage;
  String? _selectedSchool;
  String? _selectedDamageType;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _performSearch() async {
    final query = _searchController.text.trim();

    if (query.isEmpty &&
        _selectedSchool == null &&
        _selectedDamageType == null) {
      setState(() {
        _searchResults = [];
        _errorMessage = null;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _apiService.getCharacters(
        name: query.isNotEmpty ? query : null,
        school: _selectedSchool,
        damageType: _selectedDamageType,
        perPage: 50, // Get more results for search
      );

      setState(() {
        _isLoading = false;
        if (response.success && response.data != null) {
          _searchResults = response.data!;
          if (_searchResults.isEmpty) {
            _errorMessage = 'No characters found matching your criteria';
          }
        } else {
          _errorMessage = response.error ?? 'Failed to search characters';
          _searchResults = [];
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'An error occurred while searching';
        _searchResults = [];
      });
    }
  }

  void _clearFilters() {
    setState(() {
      _selectedSchool = null;
      _selectedDamageType = null;
      _searchController.clear();
      _searchResults = [];
      _errorMessage = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Search Characters',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(AppColors.primaryBlue),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          if (_selectedSchool != null ||
              _selectedDamageType != null ||
              _searchController.text.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _clearFilters,
              tooltip: 'Clear filters',
            ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchSection(),
          _buildFilterSection(),
          Expanded(child: _buildResultsSection()),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search by character name...',
          prefixIcon: const Icon(
            Icons.search,
            color: Color(AppColors.primaryBlue),
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _performSearch();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Color(AppColors.primaryBlue)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Color(AppColors.primaryBlue),
              width: 2,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        onChanged: (value) {
          // Debounce search
          Future.delayed(const Duration(milliseconds: 500), () {
            if (_searchController.text == value) {
              _performSearch();
            }
          });
        },
        onSubmitted: (value) => _performSearch(),
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: const Color(AppColors.backgroundColor),
      child: Row(
        children: [
          Expanded(child: _buildSchoolDropdown()),
          const SizedBox(width: 12),
          Expanded(child: _buildDamageTypeDropdown()),
        ],
      ),
    );
  }

  Widget _buildSchoolDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedSchool,
      decoration: InputDecoration(
        labelText: 'School',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: [
        const DropdownMenuItem<String>(value: null, child: Text('All Schools')),
        ...Schools.allSchools.map(
          (school) =>
              DropdownMenuItem<String>(value: school, child: Text(school)),
        ),
      ],
      onChanged: (value) {
        setState(() {
          _selectedSchool = value;
        });
        _performSearch();
      },
    );
  }

  Widget _buildDamageTypeDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedDamageType,
      decoration: InputDecoration(
        labelText: 'Damage Type',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: const [
        DropdownMenuItem<String>(value: null, child: Text('All Types')),
        DropdownMenuItem<String>(
          value: DamageTypes.explosive,
          child: Text(DamageTypes.explosive),
        ),
        DropdownMenuItem<String>(
          value: DamageTypes.penetration,
          child: Text(DamageTypes.penetration),
        ),
        DropdownMenuItem<String>(
          value: DamageTypes.mystic,
          child: Text(DamageTypes.mystic),
        ),
      ],
      onChanged: (value) {
        setState(() {
          _selectedDamageType = value;
        });
        _performSearch();
      },
    );
  }

  Widget _buildResultsSection() {
    if (_isLoading) {
      return const widgets.LoadingWidget(message: 'Searching characters...');
    }

    if (_errorMessage != null) {
      return widgets.EmptyStateWidget(
        title: 'No Results Found',
        subtitle: _errorMessage!,
        icon: Icons.search_off,
        onAction: _performSearch,
        actionText: 'Try Again',
      );
    }

    if (_searchResults.isEmpty) {
      return const widgets.EmptyStateWidget(
        title: 'Start Searching',
        subtitle:
            'Enter a character name or select filters to search for Blue Archive characters',
        icon: Icons.search,
      );
    }

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          color: const Color(AppColors.backgroundColor),
          child: Row(
            children: [
              Text(
                'Found ${_searchResults.length} character${_searchResults.length == 1 ? '' : 's'}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(AppColors.textPrimary),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: _searchResults.length,
            itemBuilder: (context, index) {
              return CharacterCard(
                character: _searchResults[index],
                isCompact: true,
              );
            },
          ),
        ),
      ],
    );
  }
}
