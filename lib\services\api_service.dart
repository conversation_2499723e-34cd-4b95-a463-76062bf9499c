import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/character.dart';
import '../utils/constants.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final http.Client _client = http.Client();

  // Get all characters with pagination
  Future<ApiResponse<List<Character>>> getCharacters({
    int page = 1,
    int perPage = AppConstants.defaultPageSize,
    String? name,
    String? school,
    String? damageType,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'perPage': perPage.toString(),
      };

      if (name != null && name.isNotEmpty) {
        queryParams['name'] = name;
      }
      if (school != null && school.isNotEmpty) {
        queryParams['school'] = school;
      }
      if (damageType != null && damageType.isNotEmpty) {
        queryParams['damageType'] = damageType;
      }

      final uri = Uri.parse('${ApiConstants.baseUrl}${ApiConstants.charactersEndpoint}')
          .replace(queryParameters: queryParams);

      final response = await _client.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        final List<Character> characters = jsonData
            .map((json) => Character.fromJson(json))
            .toList();

        return ApiResponse<List<Character>>(
          success: true,
          data: characters,
          currentPage: page,
          totalCount: characters.length,
        );
      } else {
        return ApiResponse<List<Character>>(
          success: false,
          error: 'Failed to load characters: ${response.statusCode}',
        );
      }
    } on SocketException {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'No internet connection',
      );
    } on http.ClientException {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'Network error occurred',
      );
    } catch (e) {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  // Get random character(s)
  Future<ApiResponse<List<Character>>> getRandomCharacters({int count = 1}) async {
    try {
      final Map<String, String> queryParams = {};
      if (count > 1) {
        queryParams['count'] = count.toString();
      }

      final uri = Uri.parse('${ApiConstants.baseUrl}${ApiConstants.randomCharacterEndpoint}')
          .replace(queryParameters: queryParams.isNotEmpty ? queryParams : null);

      final response = await _client.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final dynamic jsonData = json.decode(response.body);
        List<Character> characters;

        if (jsonData is List) {
          characters = jsonData.map((json) => Character.fromJson(json)).toList();
        } else {
          characters = [Character.fromJson(jsonData)];
        }

        return ApiResponse<List<Character>>(
          success: true,
          data: characters,
        );
      } else {
        return ApiResponse<List<Character>>(
          success: false,
          error: 'Failed to load random characters: ${response.statusCode}',
        );
      }
    } on SocketException {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'No internet connection',
      );
    } on http.ClientException {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'Network error occurred',
      );
    } catch (e) {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  // Get detailed students information
  Future<ApiResponse<List<Character>>> getStudents({
    int page = 1,
    int perPage = 4,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'perPage': perPage.toString(),
      };

      final uri = Uri.parse('${ApiConstants.baseUrl}${ApiConstants.studentsEndpoint}')
          .replace(queryParameters: queryParams);

      final response = await _client.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        final List<Character> characters = jsonData
            .map((json) => Character.fromJson(json))
            .toList();

        return ApiResponse<List<Character>>(
          success: true,
          data: characters,
          currentPage: page,
          totalCount: characters.length,
        );
      } else {
        return ApiResponse<List<Character>>(
          success: false,
          error: 'Failed to load students: ${response.statusCode}',
        );
      }
    } on SocketException {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'No internet connection',
      );
    } on http.ClientException {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'Network error occurred',
      );
    } catch (e) {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  void dispose() {
    _client.close();
  }
}
