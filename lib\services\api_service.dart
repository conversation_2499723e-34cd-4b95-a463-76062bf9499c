import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/character.dart';
import '../utils/constants.dart';
import '../data/mock_data.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final http.Client _client = http.Client();

  // Helper method to get headers that work better with CORS
  Map<String, String> get _headers => {
    'Accept': 'application/json',
    'User-Agent': 'BlueArchiveApp/1.0',
  };

  // Helper method to handle CORS issues
  String _getApiUrl(String endpoint) {
    // For web, we might need to handle CORS differently
    if (kIsWeb) {
      // Try direct API first, if it fails we'll handle it in the catch block
      return '${ApiConstants.baseUrl}$endpoint';
    }
    return '${ApiConstants.baseUrl}$endpoint';
  }

  // Get all characters with pagination
  Future<ApiResponse<List<Character>>> getCharacters({
    int page = 1,
    int perPage = AppConstants.defaultPageSize,
    String? name,
    String? school,
    String? damageType,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'perPage': perPage.toString(),
      };

      if (name != null && name.isNotEmpty) {
        queryParams['name'] = name;
      }
      if (school != null && school.isNotEmpty) {
        queryParams['school'] = school;
      }
      if (damageType != null && damageType.isNotEmpty) {
        queryParams['damageType'] = damageType;
      }

      final uri = Uri.parse(
        _getApiUrl(ApiConstants.charactersEndpoint),
      ).replace(queryParameters: queryParams);

      final response = await _client
          .get(uri, headers: _headers)
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        final List<Character> characters = jsonData
            .map((json) => Character.fromJson(json))
            .toList();

        return ApiResponse<List<Character>>(
          success: true,
          data: characters,
          currentPage: page,
          totalCount: characters.length,
        );
      } else {
        // Handle 403 CORS error by using mock data
        if (response.statusCode == 403 && kIsWeb) {
          final mockCharacters = MockData.searchCharacters(
            name: name,
            school: school,
            damageType: damageType,
          );

          // Simulate pagination
          final startIndex = (page - 1) * perPage;
          final endIndex = startIndex + perPage;
          final paginatedCharacters = mockCharacters.length > startIndex
              ? mockCharacters.sublist(
                  startIndex,
                  endIndex > mockCharacters.length
                      ? mockCharacters.length
                      : endIndex,
                )
              : <Character>[];

          return ApiResponse<List<Character>>(
            success: true,
            data: paginatedCharacters,
            currentPage: page,
            totalCount: paginatedCharacters.length,
          );
        }

        String errorMessage =
            'Failed to load characters: ${response.statusCode}';
        if (response.statusCode == 403) {
          errorMessage =
              'Access denied. This might be a CORS issue when running on web.';
        } else if (response.statusCode == 404) {
          errorMessage =
              'API endpoint not found. Please check your internet connection.';
        } else if (response.statusCode >= 500) {
          errorMessage = 'Server error. Please try again later.';
        }

        return ApiResponse<List<Character>>(
          success: false,
          error: errorMessage,
        );
      }
    } on SocketException {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'No internet connection',
      );
    } on http.ClientException {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'Network error occurred',
      );
    } catch (e) {
      // If we're on web and getting CORS errors, use mock data as fallback
      if (kIsWeb && e.toString().contains('XMLHttpRequest')) {
        final mockCharacters = MockData.searchCharacters(
          name: name,
          school: school,
          damageType: damageType,
        );

        // Simulate pagination
        final startIndex = (page - 1) * perPage;
        final endIndex = startIndex + perPage;
        final paginatedCharacters = mockCharacters.length > startIndex
            ? mockCharacters.sublist(
                startIndex,
                endIndex > mockCharacters.length
                    ? mockCharacters.length
                    : endIndex,
              )
            : <Character>[];

        return ApiResponse<List<Character>>(
          success: true,
          data: paginatedCharacters,
          currentPage: page,
          totalCount: paginatedCharacters.length,
        );
      }

      return ApiResponse<List<Character>>(
        success: false,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  // Get random character(s)
  Future<ApiResponse<List<Character>>> getRandomCharacters({
    int count = 1,
  }) async {
    try {
      final Map<String, String> queryParams = {};
      if (count > 1) {
        queryParams['count'] = count.toString();
      }

      final uri = Uri.parse(
        _getApiUrl(ApiConstants.randomCharacterEndpoint),
      ).replace(queryParameters: queryParams.isNotEmpty ? queryParams : null);

      final response = await _client
          .get(uri, headers: _headers)
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final dynamic jsonData = json.decode(response.body);
        List<Character> characters;

        if (jsonData is List) {
          characters = jsonData
              .map((json) => Character.fromJson(json))
              .toList();
        } else {
          characters = [Character.fromJson(jsonData)];
        }

        return ApiResponse<List<Character>>(success: true, data: characters);
      } else {
        return ApiResponse<List<Character>>(
          success: false,
          error: 'Failed to load random characters: ${response.statusCode}',
        );
      }
    } on SocketException {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'No internet connection',
      );
    } on http.ClientException {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'Network error occurred',
      );
    } catch (e) {
      // If we're on web and getting CORS errors, use mock data as fallback
      if (kIsWeb && e.toString().contains('XMLHttpRequest')) {
        final mockCharacters = MockData.getRandomCharacters(count);
        return ApiResponse<List<Character>>(
          success: true,
          data: mockCharacters,
        );
      }

      return ApiResponse<List<Character>>(
        success: false,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  // Get detailed students information
  Future<ApiResponse<List<Character>>> getStudents({
    int page = 1,
    int perPage = 4,
  }) async {
    try {
      final Map<String, String> queryParams = {
        'page': page.toString(),
        'perPage': perPage.toString(),
      };

      final uri = Uri.parse(
        _getApiUrl(ApiConstants.studentsEndpoint),
      ).replace(queryParameters: queryParams);

      final response = await _client
          .get(uri, headers: _headers)
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);
        final List<Character> characters = jsonData
            .map((json) => Character.fromJson(json))
            .toList();

        return ApiResponse<List<Character>>(
          success: true,
          data: characters,
          currentPage: page,
          totalCount: characters.length,
        );
      } else {
        return ApiResponse<List<Character>>(
          success: false,
          error: 'Failed to load students: ${response.statusCode}',
        );
      }
    } on SocketException {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'No internet connection',
      );
    } on http.ClientException {
      return ApiResponse<List<Character>>(
        success: false,
        error: 'Network error occurred',
      );
    } catch (e) {
      // If we're on web and getting CORS errors, use mock data as fallback
      if (kIsWeb && e.toString().contains('XMLHttpRequest')) {
        final mockCharacters = MockData.getMockCharacters();

        // Simulate pagination for students
        final startIndex = (page - 1) * perPage;
        final endIndex = startIndex + perPage;
        final paginatedCharacters = mockCharacters.length > startIndex
            ? mockCharacters.sublist(
                startIndex,
                endIndex > mockCharacters.length
                    ? mockCharacters.length
                    : endIndex,
              )
            : <Character>[];

        return ApiResponse<List<Character>>(
          success: true,
          data: paginatedCharacters,
          currentPage: page,
          totalCount: paginatedCharacters.length,
        );
      }

      return ApiResponse<List<Character>>(
        success: false,
        error: 'An unexpected error occurred: $e',
      );
    }
  }

  void dispose() {
    _client.close();
  }
}
